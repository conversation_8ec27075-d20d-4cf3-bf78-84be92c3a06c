// Simple test script to verify streaming functionality
// This can be run in the browser console to test the streaming implementation

console.log('Testing Ollama streaming functionality...');

// Test the streaming callback
const testStreamingCallback = (chunk) => {
  console.log('Received chunk:', chunk);
};

// Test the processMessage function with streaming
async function testStreaming() {
  try {
    // Import the necessary modules (this would work in the browser context)
    const { processMessage } = await import('./src/utils/chatService.ts');
    
    // Create a test conversation
    const testConversation = {
      id: 'test',
      messages: [],
      title: 'Test Conversation',
      createdAt: Date.now(),
      lastModified: Date.now()
    };
    
    console.log('Starting streaming test...');
    
    // Test with a simple message
    const response = await processMessage(
      testConversation,
      'Hello, can you tell me a short joke?',
      'ollama-llama2', // Assuming llama2 is available
      testStreamingCallback
    );
    
    console.log('Final response:', response);
    console.log('Streaming test completed successfully!');
    
  } catch (error) {
    console.error('Streaming test failed:', error);
  }
}

// Export for manual testing
if (typeof window !== 'undefined') {
  window.testStreaming = testStreaming;
  console.log('Test function available as window.testStreaming()');
}
