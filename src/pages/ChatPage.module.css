.chat-page-container {
  display: flex;
  height: 100vh;
  overflow: hidden;
  background: var(--background-primary, #fafafa);
}

.feature-tip {
  margin-top: 30px;
  background-color: #e8f5e9;
  padding: 20px;
  border-radius: 8px;
  max-width: 500px;
  text-align: left;
  color: #333;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  pointer-events: auto;
  z-index: 10;
}

.feature-tip h3 {
  margin-top: 0;
  color: #2e7d32;
  font-size: 18px;
}

.feature-tip code {
  background-color: #f1f8e9;
  padding: 3px 6px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 14px;
  border: 1px solid #c8e6c9;
}

.helper-text {
  margin-top: 20px;
  color: var(--primary-color, #4f46e5);
  font-weight: bold;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

.chat-main-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100vh;
  position: relative;
  transition: margin 0.3s ease;
  overflow: hidden;
}

.chat-header {
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--background-secondary);
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
}

.panel-toggle-button {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.panel-toggle-button:hover {
  background-color: var(--background-tertiary);
  color: var(--text-primary);
}

.model-selector-container {
  flex: 1;
  display: flex;
  justify-content: center;
  position: relative;
}

.model-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  cursor: pointer;
  user-select: none;
  background-color: var(--background-secondary);
  transition: background-color 0.2s;
  min-width: 200px;
  justify-content: space-between;
  position: relative;
}

.model-selector:hover {
  background-color: var(--background-tertiary);
}

.model-selector.active {
  background-color: var(--background-tertiary);
}

.dropdown-arrow {
  font-size: 12px;
  opacity: 0.7;
}

.model-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  margin-top: 4px;
  background-color: var(--background-secondary);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
}

.model-option {
  padding: 8px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 14px;
}

.model-option:hover {
  background-color: var(--background-tertiary);
}

.model-option.selected {
  background-color: var(--background-tertiary);
  color: var(--accent-coral);
}

.new-tag {
  font-size: 10px;
  padding: 2px 6px;
  background-color: var(--accent-coral);
  color: var(--background-primary);
  border-radius: 12px;
  margin-left: 8px;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  scroll-behavior: smooth;
  padding-bottom: 120px;
  margin-bottom: 80px;
}

.empty-chat-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  text-align: center;
  color: var(--text-secondary, #666);
  margin-top: 40px;
  padding-bottom: 100px;
  font-size: 15px;
  position: absolute;
  top: 50px;
  left: 0;
  right: 0;
  pointer-events: auto;
  z-index: 5;
}

.message {
  display: flex;
  flex-direction: column;
  max-width: 90%;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 15px;
  line-height: 1.5;
}

.user-message {
  align-self: flex-end;
  background-color: var(--primary-color, #4f46e5);
  color: white;
  margin-left: 20%;
}

.assistant-message {
  align-self: flex-start;
  background-color: var(--background-secondary, #fff);
  border: 1px solid var(--border-color, #eee);
  color: var(--text-primary, #333);
  margin-right: 20%;
}

.message-content {
  white-space: pre-wrap;
  word-break: break-word;
}

.system-message {
  background-color: #f5f0ff;
  border-left: 4px solid #7c4dff;
  padding: 12px 16px;
  border-radius: 8px;
  margin: 16px 0;
  color: #333;
  font-weight: 500;
}

.input-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background: var(--background-primary, #fafafa);
  display: flex;
  align-items: flex-start;
  gap: 12px;
  border-top: 1px solid var(--border-color, #eee);
  z-index: 10;
}

.input-container textarea {
  flex: 1;
  min-height: 24px;
  max-height: 200px;
  padding: 12px;
  border: 2px solid var(--primary-color, #4f46e5);
  border-radius: 8px;
  background: var(--background-secondary, #fff);
  color: var(--text-primary, #333);
  font-size: 15px;
  line-height: 1.5;
  resize: none;
  overflow-y: auto;
  outline: none;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.input-container textarea:focus {
  outline: none;
  border-color: var(--primary-color, #4f46e5);
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.1);
}

.send-button {
  padding: 16px 24px;
  background: var(--primary-color, #4f46e5);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 18px;
  transition: background-color 0.2s;
  align-self: stretch;
}

.send-button:hover:not(:disabled) {
  background: var(--primary-color-dark, #4338ca);
}

.send-button:disabled {
  background: var(--background-disabled, #e5e7eb);
  cursor: not-allowed;
}

/* Memory Context Indicator */
.memory-context-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  margin: 0 1rem 0.5rem 1rem;
  background-color: var(--background-secondary, #fff);
  border: 1px solid var(--border-color, #eee);
  border-radius: 8px;
  font-size: 0.8rem;
  color: var(--text-secondary, #666);
  position: absolute;
  bottom: 80px;
  left: 0;
  right: 0;
  z-index: 9;
}

.memory-context-text {
  font-weight: 500;
  color: var(--text-primary, #333);
}

.memory-limit-text {
  color: var(--text-secondary, #666);
  font-style: italic;
}

/* Streaming cursor animation */
.streaming-cursor {
  display: inline-block;
  background-color: var(--text-primary, #333);
  color: var(--text-primary, #333);
  animation: blink 1s infinite;
  margin-left: 2px;
  font-weight: bold;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* Streaming message specific styles */
.streaming-message {
  position: relative;
}

.streaming-message .message-content {
  min-height: 20px; /* Ensure there's space for the cursor even with no content */
}