import React, { useState, useEffect, useRef } from 'react';
import styles from '../pages/ChatPage.module.css';

interface StreamingMessageProps {
  id: string;
  initialContent?: string;
  isStreaming: boolean;
  onStreamingComplete?: (finalContent: string) => void;
}

export const StreamingMessage: React.FC<StreamingMessageProps> = ({
  id,
  initialContent = '',
  isStreaming,
  onStreamingComplete
}) => {
  const [content, setContent] = useState(initialContent);
  const [isCurrentlyStreaming, setIsCurrentlyStreaming] = useState(isStreaming);
  const contentRef = useRef(content);
  const streamingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Update content ref when content changes
  useEffect(() => {
    contentRef.current = content;
  }, [content]);

  // Handle streaming completion
  useEffect(() => {
    if (!isStreaming && isCurrentlyStreaming) {
      setIsCurrentlyStreaming(false);
      if (onStreamingComplete) {
        onStreamingComplete(contentRef.current);
      }
    }
  }, [isStreaming, isCurrentlyStreaming, onStreamingComplete]);

  // Method to append new content (called from parent)
  const appendContent = (newChunk: string) => {
    setContent(prev => prev + newChunk);
    setIsCurrentlyStreaming(true);
    
    // Clear any existing timeout
    if (streamingTimeoutRef.current) {
      clearTimeout(streamingTimeoutRef.current);
    }
    
    // Set a timeout to detect when streaming has stopped
    streamingTimeoutRef.current = setTimeout(() => {
      setIsCurrentlyStreaming(false);
      if (onStreamingComplete) {
        onStreamingComplete(contentRef.current);
      }
    }, 1000); // 1 second timeout
  };

  // Expose the appendContent method to parent components
  useEffect(() => {
    // Store the append function on the element for external access
    const element = document.getElementById(`streaming-message-${id}`);
    if (element) {
      (element as any).appendContent = appendContent;
    }
  }, [id]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (streamingTimeoutRef.current) {
        clearTimeout(streamingTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div
      id={`streaming-message-${id}`}
      className={`${styles['message']} ${styles['assistant-message']}`}
    >
      <div className={styles['message-content']}>
        {content}
        {isCurrentlyStreaming && (
          <span className={styles['streaming-cursor']}>▋</span>
        )}
      </div>
    </div>
  );
};

// Hook to manage streaming messages
export const useStreamingMessage = (messageId: string) => {
  const [isStreaming, setIsStreaming] = useState(false);
  const [content, setContent] = useState('');

  const startStreaming = () => {
    setIsStreaming(true);
    setContent('');
  };

  const appendChunk = (chunk: string) => {
    setContent(prev => prev + chunk);
    const element = document.getElementById(`streaming-message-${messageId}`);
    if (element && (element as any).appendContent) {
      (element as any).appendContent(chunk);
    }
  };

  const stopStreaming = () => {
    setIsStreaming(false);
  };

  return {
    isStreaming,
    content,
    startStreaming,
    appendChunk,
    stopStreaming
  };
};

export default StreamingMessage;
